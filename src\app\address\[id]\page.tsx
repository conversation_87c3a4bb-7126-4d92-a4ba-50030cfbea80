"use client"

import { AddressOverview } from "@/app/address/components/address-overview"
import { NFTTransfersTable } from "@/app/address/components/nft-transfer-table"
import { ProducedBlocksTable } from "@/app/address/components/produced-block-table"
import { TokenTransfersTable } from "@/app/address/components/token-transfer-table"
import { ContractTab } from "@/components/contract/contract-tab"
import { CSVExportButton } from "@/components/csv-button-export"
import { TransactionsTable } from "@/components/transaction-table"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Skeleton } from "@/components/ui/skeleton"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useGetBalance, useTokenBalance } from "@/hooks/useTokens"
import {
  useTransactionCountByAddress,
  useTransactionList,
} from "@/hooks/useTransactions"
import {
  useGetAccountInfo,
  useGetCode,
  useGetImplementationAddress,
  useGetAbi,
} from "@/hooks/useVerifyContract"
import { formatTransactionsForCSV } from "@/lib/utils/csv-export"
import { AccountTypeEnum } from "@/types/verify-contract"
import { getAddress } from "ethers"
import { useParams } from "next/navigation"
import { useEffect, useState, useMemo } from "react"
import { AddressCrons } from "../components/crons-tab/address-crons"
import InternalTransactionTab from "../components/internal-transactions-tab"
import { AddressOverviewSkeleton } from "./loading/address-overview-skeleton"
import { AnalyticsChartSkeleton } from "./loading/analytics-chart-skeleton"
import { ContractTabSkeleton } from "./loading/contract-tab-skeleton"
import { NFTTransfersTableSkeleton } from "./loading/nft-transfers-table-skeleton"
import { TokenTransfersTableSkeleton } from "./loading/token-transfers-tableskeleton"
import { TransactionsTableSkeleton } from "./loading/transactions-table-skeleton"
import { Check } from "lucide-react"

export default function AddressPage() {
  const params = useParams()
  const id = params.id as string
  const [validatedAddress, setValidatedAddress] = useState<string>("")
  const [validatedImplAddress, setValidatedImplAddress] = useState<string>("-")
  const [isContractVerified, setIsContractVerified] = useState<boolean>(false)

  // Validate the address
  useEffect(() => {
    if (id) {
      try {
        // Validate and normalize the Ethereum address
        const normalizedAddress = getAddress(id)
        setValidatedAddress(normalizedAddress)
      } catch (error) {
        console.error("Invalid address:", error)
        setValidatedAddress(id) // Fallback to the original address if invalid
      }
    }
  }, [id])

  const { data: balance, isLoading: isBalanceLoading } = useGetBalance(
    validatedAddress || id,
  )
  const { data: transactionCount, isLoading: isTransactionCountLoading } =
    useTransactionCountByAddress(validatedAddress || id)
  const { data: tokenHoldings, isLoading: isTokenHoldingsLoading } =
    useTokenBalance(validatedAddress || id)
  const { data: code, isLoading: isCodeLoading } = useGetCode(
    validatedAddress || id,
  )
  const { data: transactions, isLoading: isTransactionsLoading } =
    useTransactionList({
      page: 1,
      limit: 25,
      fromAddress: validatedAddress || id,
      toAddress: validatedAddress || id,
    })

  const { data: accountInfo, isLoading: isAccountInfoLoading } =
    useGetAccountInfo(validatedAddress || id)
  const { data: abi, isLoading: isAbiLoading } = useGetAbi(
    validatedAddress || id,
  )

  const { data: implementationAddress, isLoading: isImplementationLoading } =
    useGetImplementationAddress(validatedAddress || id, accountInfo?.isProxy)

  // Validate implementation address
  useEffect(() => {
    if (implementationAddress) {
      try {
        const normalizedAddress = getAddress(implementationAddress)
        setValidatedImplAddress(normalizedAddress)
      } catch (error) {
        console.error("Invalid implementation address:", error)
        setValidatedImplAddress("-")
      }
    } else {
      setValidatedImplAddress("-")
    }
  }, [implementationAddress])

  useEffect(() => {
    if (abi && abi !== "0x" && code !== "0x") {
      try {
        JSON.parse(abi)
        setIsContractVerified(true)
      } catch (error) {
        setIsContractVerified(false)
      }
    } else {
      setIsContractVerified(false)
    }
  }, [abi, code])

  const transactionDisplayText = useMemo(() => {
    if (isTransactionCountLoading) {
      return (
        <span>
          Latest <Skeleton className="h-4 w-8 inline-block" /> from a total of{" "}
          <Skeleton className="h-4 w-16 inline-block" /> <Skeleton className="h-4 w-20 inline-block" />
        </span>
      )
    }

    const totalCount = transactionCount ? Number.parseInt(transactionCount) : 0
    const displayedCount = transactions?.result?.data?.length || 0

    return `Latest ${displayedCount} from a total of ${totalCount.toLocaleString()} ${totalCount === 1 ? 'transaction' : 'transactions'}`
  }, [
    isTransactionCountLoading,
    transactionCount,
    transactions?.result?.data?.length,
  ])

  const isOverviewLoading =
    isBalanceLoading || isTransactionCountLoading || isTokenHoldingsLoading
  const isContractLoading =
    isCodeLoading || isAccountInfoLoading || isImplementationLoading

  return (
    <div className="container mx-auto px-4 py-8">
      {isOverviewLoading ? (
        <AddressOverviewSkeleton />
      ) : (
        <AddressOverview
          balance={balance}
          transactionCount={transactionCount}
          tokenHoldings={tokenHoldings?.Balances ?? []}
          address={validatedAddress || id}
          accountInfo={accountInfo}
        />
      )}

      <div className="mt-8">
        <Alert>
          <AlertDescription>
            {code !== "0x"
              ? accountInfo?.isProxy
                ? "This is a proxy contract. The implementation logic is at a separate address but called through this contract."
                : "A contract address hosts a smart contract, which is a set of code stored on the blockchain that runs when predetermined conditions are met."
              : "A wallet address is a publicly available address that allows its owner to receive funds from another party. To access the funds in an address, you must have its private key."}
          </AlertDescription>
        </Alert>
      </div>

      <Tabs defaultValue="transactions" className="mt-8">
        <TabsList className="w-full justify-start">
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
          <TabsTrigger value="internal-txns">Internal Txns</TabsTrigger>
          <TabsTrigger value="token-transfers">
            Token Transfers (HRC-20)
          </TabsTrigger>
          {accountInfo?.accountType === AccountTypeEnum.ADDRESS && (
            <TabsTrigger value="crons">Crons</TabsTrigger>
          )}
          <TabsTrigger value="nft-transfers">NFT Transfers</TabsTrigger>
          {!isCodeLoading && code !== "0x" && (
            <TabsTrigger value="contract" className="relative">
              Contract
              {isContractVerified && (
                <div className="absolute -top-1.5 -right-1">
                  <div className="h-4 w-4 rounded-full bg-green-500 flex items-center justify-center">
                    <Check className="h-2.5 w-2.5 text-white" />
                  </div>
                </div>
              )}
            </TabsTrigger>
          )}
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="produced-blocks">Produced Blocks</TabsTrigger>
        </TabsList>

        <TabsContent value="transactions" className="mt-4">
          <div className="my-4 flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              {transactionDisplayText}
            </div>
            <CSVExportButton
              data={transactions?.result?.data ?? []}
              formatter={formatTransactionsForCSV}
              filename={`address-${validatedAddress || id}-transactions.csv`}
              disabled={
                isTransactionsLoading || !transactions?.result?.data?.length
              }
            />
          </div>
          <div className="card">
            <div className="border rounded-lg">
              {isTransactionsLoading ? (
                <TransactionsTableSkeleton />
              ) : (
                <TransactionsTable
                  transactions={transactions?.result?.data ?? []}
                  showExport={false}
                  mapNameTag={transactions?.mapNameTag}
                />
              )}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="internal-txns" className="mt-4">
          <div className="card">
            <InternalTransactionTab />
          </div>
        </TabsContent>

        <TabsContent value="token-transfers" className="mt-4">
          <div className="space-y-4">
            <Alert>
              <AlertDescription>
                Transactions involving tokens marked as suspicious, unsafe, spam
                or brand infringement are currently hidden. To show them, go to
                Site Settings.
              </AlertDescription>
            </Alert>
            <div className="card">
              <div className="border rounded-lg">
                <TokenTransfersTable
                  address={validatedAddress || id}
                  loadingFallback={<TokenTransfersTableSkeleton />}
                />
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="nft-transfers" className="mt-4">
          <div className="card">
            <div className="border rounded-lg">
              <NFTTransfersTable
                transfers={[]}
                loadingFallback={<NFTTransfersTableSkeleton />}
              />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="crons" className="mt-4">
          <div className="card">
            <AddressCrons address={validatedAddress || id} />
          </div>
        </TabsContent>

        {!isCodeLoading && code !== "0x" && (
          <TabsContent value="contract" className="mt-4">
            <div className="card">
              {isContractLoading ? (
                <ContractTabSkeleton />
              ) : (
                <ContractTab
                  address={validatedAddress || id}
                  isProxy={accountInfo?.isProxy}
                  implementationAddress={validatedImplAddress}
                  isVerified={isContractVerified}
                />
              )}
            </div>
          </TabsContent>
        )}

        <TabsContent value="analytics" className="mt-4">
          <AnalyticsChartSkeleton />
        </TabsContent>

        <TabsContent value="produced-blocks" className="mt-4">
          <div className="card">
            <div className="border rounded-lg">
              <ProducedBlocksTable blocks={[]} />
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
