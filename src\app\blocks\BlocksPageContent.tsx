"use client"

import { BlocksTable } from "@/components/blocks-table"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { PaginationWithLinks } from "@/components/ui/pagination-with-links"
import { Skeleton } from "@/components/ui/skeleton"
import { PATH_ROUTER } from "@/constants/routers"
import { useBlockList } from "@/hooks/useBlocks"
import { AlertCircle } from "lucide-react"
import Link from "next/link"
import { useSearchParams } from "next/navigation"
import { numericFormatter } from "react-number-format"

export default function BlocksPageContent() {
  const searchParams = useSearchParams()
  const page = Math.max(1, Number(searchParams.get("page")) || 1)
  const limit = 25

  const {
    data: blocks,
    isLoading,
    isError,
    error,
  } = useBlockList({
    page,
    limit,
  })

  if (isError) {
    return (
      <Card className="w-full">
        <CardContent className="pt-6">
          <div className="flex flex-col items-center justify-center p-6 text-center">
            <AlertCircle className="h-10 w-10 text-red-500 mb-4" />
            <h3 className="text-lg font-semibold">Error Loading Blocks</h3>
            <p className="text-sm text-muted-foreground mt-2">
              {error instanceof Error
                ? error.message
                : "An unexpected error occurred"}
            </p>
            <Button asChild className="mt-4">
              <Link href={`${PATH_ROUTER.BLOCKS}?page=1`}>Try Again</Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">Blocks</h1>
        <div className="text-sm text-muted-foreground">
          {!isLoading && blocks?.result?.metadata ? (
            <>
              More than{" "}
              {numericFormatter(`${blocks?.result?.metadata.total}`, {
                thousandSeparator: true,
                decimalScale: 0,
              })}{" "}
              {blocks?.result?.metadata.total === 1 ? 'block' : 'blocks'} found
            </>
          ) : (
            <Skeleton className="h-5 w-40" />
          )}
        </div>
      </div>

      {isLoading ? (
        <div className="card mb-4">
          <div className="border rounded-lg mb-4">
            <LoadingBlocksTable />
          </div>
        </div>
      ) : !blocks?.result.data || blocks?.result?.data.length === 0 ? (
        <div className="flex flex-col items-center justify-center p-6 text-center border rounded-lg mb-4">
          <h3 className="text-lg font-semibold">No Blocks Found</h3>
          <p className="text-sm text-muted-foreground mt-2">
            There are no blocks available to display.
          </p>
        </div>
      ) : (
        <div className="card mb-4">
          <div className="border rounded-lg">
            <BlocksTable
              blocks={blocks?.result.data}
              mapNameTag={blocks?.mapNameTag}
              showExport={true}
              exportFilename="blocks.csv"
            />
          </div>
        </div>
      )}

      {!isLoading && blocks?.result.metadata && (
        <PaginationWithLinks
          page={page}
          pageSize={limit}
          totalCount={blocks?.result.metadata.total || 0}
          pageSearchParam="page"
          baseUrl={PATH_ROUTER.BLOCKS}
        />
      )}
    </div>
  )
}

function LoadingBlocksTable() {
  return (
    <div className="w-full overflow-auto">
      <table className="w-full caption-bottom text-sm">
        <thead className="[&_tr]:border-b">
          <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
              Block
            </th>
            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
              Age
            </th>
            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
              Txn
            </th>
            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
              Gas Used
            </th>
            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
              Gas Limit
            </th>
            <th className="h-12 px-4 text-right align-middle font-medium text-muted-foreground">
              Reward
            </th>
          </tr>
        </thead>
        <tbody className="[&_tr:last-child]:border-0">
          {Array.from({ length: 10 }).map((_, index) => (
            <tr
              key={index}
              className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"
            >
              <td className="p-4 align-middle">
                <Skeleton className="h-5 w-24" />
              </td>
              <td className="p-4 align-middle">
                <Skeleton className="h-5 w-20" />
              </td>
              <td className="p-4 align-middle">
                <Skeleton className="h-5 w-12" />
              </td>
              <td className="p-4 align-middle">
                <Skeleton className="h-5 w-24" />
              </td>
              <td className="p-4 align-middle">
                <Skeleton className="h-5 w-24" />
              </td>
              <td className="p-4 align-middle text-right">
                <Skeleton className="h-5 w-20 ml-auto" />
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}
