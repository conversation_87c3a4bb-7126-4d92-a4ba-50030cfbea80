"use client";

import { CSVExportButton } from "@/components/csv-button-export";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { PATH_ROUTER } from "@/constants/routers";
import { truncateAddress } from "@/helpers/format";
import { formatTopStatsForCSV } from "@/lib/utils/csv-export";
import type {
  StatsTopResult,
  StatsTopTokenEntries,
  StatsTopType,
  TokenData,
} from "@/types/charts";
import { ExternalLink } from "lucide-react";
import Link from "next/link";

const TokenTable = ({
  tokens,
  valueLabel,
  valueKey,
  showPercentage = false,
  showExport = false,
  exportFilename,
}: {
  tokens: TokenData[];
  valueLabel: string;
  valueKey: "count" | "percentage";
  showPercentage?: boolean;
  showExport?: boolean;
  exportFilename?: string;
}) => {
  if (!tokens || tokens.length === 0) {
    return (
      <div className="text-center text-muted-foreground py-8">
        No data available
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {showExport && (
        <div className="flex justify-end">
          <CSVExportButton
            data={tokens}
            formatter={(data) => formatTopStatsForCSV(data, valueLabel)}
            filename={exportFilename || `${valueLabel.toLowerCase().replace(/\s+/g, "-")}.csv`}
          />
        </div>
      )}
      <div className="overflow-x-auto">
      <table className="w-full">
        <thead>
          <tr className="border-b text-xs text-muted-foreground">
            <th className="text-left py-3 font-medium">#</th>
            <th className="text-left py-3 font-medium">Token</th>
            <th className="text-right py-3 font-medium">{valueLabel}</th>
            {showPercentage && (
              <th className="text-right py-3 font-medium w-1/4">%</th>
            )}
          </tr>
        </thead>
        <tbody>
          {tokens.map((token) => (
            <tr
              key={token.rank}
              className="border-b border-muted/30 hover:bg-muted/5 transition-colors"
            >
              <td className="py-3 text-sm">{token.rank}</td>
              <td className="py-3">
                <div className="flex flex-col">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="font-mono text-xs">
                      {token.symbol}
                    </Badge>
                    <span className="text-sm font-medium">{token.name}</span>
                  </div>
                  <Link
                    href={PATH_ROUTER.TOKEN_DETAIL(token.address)}
                    className="text-xs text-blue-500 hover:underline flex items-center gap-1 mt-1"
                    title={`${token.address}`}
                  >
                    {truncateAddress(token.address)}
                    <ExternalLink size={12} />
                  </Link>
                </div>
              </td>
              <td className="py-3 text-right font-medium">
                {token[valueKey]?.toLocaleString()}
              </td>
              {showPercentage && (
                <td className="py-3 px-2">
                  <div className="flex items-center gap-2">
                    <Progress
                      value={Number.parseFloat(token.percentage || "0")}
                      className="h-2"
                    />
                    <span className="text-xs text-muted-foreground whitespace-nowrap">
                      {token.percentage}%
                    </span>
                  </div>
                </td>
              )}
            </tr>
          ))}
        </tbody>
      </table>
      </div>
    </div>
  );
};

interface TokenStatsProps {
  data: StatsTopResult<typeof StatsTopType.TOKEN>;
}

export function TokenStats({ data }: TokenStatsProps) {
  if (!data || !data.entries) {
    return (
      <div className="text-center text-muted-foreground">
        No token data available
      </div>
    );
  }

  const entries = data.entries as StatsTopTokenEntries;
  const dateRange =
    data.snapshotStart && data.snapshotEnd
      ? `${new Date(data.snapshotStart).toLocaleDateString()} - ${new Date(
          data.snapshotEnd,
        ).toLocaleDateString()}`
      : "";

  return (
    <div className="space-y-6">
      {dateRange && (
        <div className="text-sm text-muted-foreground">
          Data period: {dateRange}
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">
              Top Tokens by Unique Senders
            </CardTitle>
          </CardHeader>
          <CardContent>
            <TokenTable
              tokens={entries?.topTokensByUniqueSenders || []}
              valueLabel="Unique Senders"
              valueKey="count"
              showExport={true}
              exportFilename="top-tokens-unique-senders.csv"
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">
              Top Tokens by Unique Receivers
            </CardTitle>
          </CardHeader>
          <CardContent>
            <TokenTable
              tokens={entries?.topTokensByUniqueReceivers || []}
              valueLabel="Unique Receivers"
              valueKey="count"
              showExport={true}
              exportFilename="top-tokens-unique-receivers.csv"
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">
              Top Tokens by Total Uniques
            </CardTitle>
          </CardHeader>
          <CardContent>
            <TokenTable
              tokens={entries?.topTokensByUniqueTotal || []}
              valueLabel="Total Uniques"
              valueKey="count"
              showExport={true}
              exportFilename="top-tokens-total-uniques.csv"
            />
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">
            Top Tokens by Transaction Count
          </CardTitle>
        </CardHeader>
        <CardContent>
          <TokenTable
            tokens={entries?.topTokensByTxCount || []}
            valueLabel="Transaction Count"
            valueKey="count"
            showPercentage={true}
            showExport={true}
            exportFilename="top-tokens-transaction-count.csv"
          />
        </CardContent>
      </Card>
    </div>
  );
}
