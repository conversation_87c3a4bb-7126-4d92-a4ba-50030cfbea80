import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  <PERSON><PERSON>ip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { ZERO_ADDRESS } from "@/constants"
import { PATH_ROUTER } from "@/constants/routers"
import { formatHash, formatTimestamp, normalizeAddress, weiToGwei } from "@/helpers/format"
import { getNameTag, renderAddressWithTag } from "@/helpers/nameTags"
import type { BlockList } from "@/types/blocks"
import { CuboidIcon as CubeIcon } from "lucide-react"
import Link from "next/link"
import { numericFormatter } from "react-number-format"

interface BlocksTableProps {
  blocks: BlockList[] | undefined
  mapNameTag?: { [address: string]: string }
}

export function BlocksTable({ blocks, mapNameTag }: BlocksTableProps) {
  // Function to calculate gas usage percentage
  const calculateGasPercentage = (
    gasUsed: string,
    gasLimit: string,
  ): number => {
    const used = Number.parseInt(gasUsed, 16)
    const limit = Number.parseInt(gasLimit, 16)
    return (used * 100) / limit
  }

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Block</TableHead>
            <TableHead>Epoch</TableHead>
            <TableHead>Hash</TableHead>
            <TableHead>Age</TableHead>
            <TableHead>Miner</TableHead>
            <TableHead>Gas Used (%)</TableHead>
            <TableHead className="text-right">Txn</TableHead>
            <TableHead>Base Fee</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {blocks?.map((block) => {
            const minerNameTag = getNameTag(block.miner, mapNameTag)

            return (
              <TableRow key={block.id}>
                <TableCell>
                  <Link
                    href={PATH_ROUTER.BLOCK_DETAIL(block.number)}
                    className="flex items-center gap-2 text-blue-700 hover:underline"
                  >
                    <CubeIcon className="h-4 w-4" />
                    {block.number}
                  </Link>
                </TableCell>
                <TableCell>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Link
                          href={PATH_ROUTER.EPOCH_DETAIL(block.epochNumber)}
                          className="flex items-center gap-2 text-blue-700 hover:underline"
                        >
                          {block.epochNumber}
                        </Link>
                      </TooltipTrigger>
                      <TooltipContent>
                        Epoch: {block.epochNumber}
                        {block.validators && block.validators.length > 0 && (
                          <>
                            <br />
                            Validators: {block.validators.length}
                          </>
                        )}
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </TableCell>
                <TableCell>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        {formatHash(block.hash, 6, 6)}
                      </TooltipTrigger>
                      <TooltipContent>{block.hash}</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </TableCell>
                <TableCell>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        {formatTimestamp(block?.timestamp)}
                      </TooltipTrigger>
                      <TooltipContent>{block?.timestamp}</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </TableCell>
                <TableCell>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Link
                          href={PATH_ROUTER.ADDRESS_DETAIL(
                            block.miner || ZERO_ADDRESS,
                          )}
                          className="text-blue-700 hover:underline"
                        >
                          {renderAddressWithTag(block.miner, mapNameTag, true)}
                        </Link>
                      </TooltipTrigger>
                      <TooltipContent>
                        <div className="flex flex-col items-center">
                          {minerNameTag && (
                            <div className="font-medium">{minerNameTag}</div>
                          )}
                          <div>{normalizeAddress(block.miner) || ZERO_ADDRESS}</div>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </TableCell>
                <TableCell>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        {numericFormatter(
                          Number.parseInt(block.gasUsed, 16).toFixed(0),
                          {
                            thousandSeparator: true,
                            decimalScale: 0,
                          },
                        )}{" "}
                        (
                        {calculateGasPercentage(
                          block.gasUsed,
                          block.gasLimit,
                        ).toFixed(2)}
                        %)
                      </TooltipTrigger>
                      <TooltipContent>
                        Gas Used:{" "}
                        {numericFormatter(
                          Number.parseInt(block.gasUsed, 16).toFixed(0),
                          {
                            thousandSeparator: true,
                            decimalScale: 0,
                          },
                        )}
                        <br />
                        Gas Limit:{" "}
                        {numericFormatter(
                          Number.parseInt(block.gasLimit, 16).toFixed(0),
                          {
                            thousandSeparator: true,
                            decimalScale: 0,
                          },
                        )}
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </TableCell>
                <TableCell className="text-right">
                  <Link
                    href={`/txs?block=${block.number}`}
                    className="text-blue-700 hover:underline"
                  >
                    {block.transactionCount}
                  </Link>
                </TableCell>
                <TableCell>
                  {block.baseFeePerGas ? (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger>
                          {weiToGwei(block.baseFeePerGas)} Gwei
                        </TooltipTrigger>
                        <TooltipContent>{block.baseFeePerGas}</TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  ) : (
                    "N/A"
                  )}
                </TableCell>
              </TableRow>
            )
          })}
        </TableBody>
      </Table>
    </div>
  )
}
