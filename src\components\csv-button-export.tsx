"use client";

import { But<PERSON>, type ButtonProps } from "@/components/ui/button";
import { exportToCSV } from "@/lib/utils/csv-export";
import { Download } from "lucide-react";
import { useState } from "react";

interface CSVExportButtonProps extends Omit<ButtonProps, "onClick"> {
  data: any[];
  formatter?: (data: any[]) => any[];
  headers?: { key: string; label: string }[];
  filename?: string;
  onExportStart?: () => void;
  onExportComplete?: () => void;
  onExportError?: (error: Error) => void;
}

export function CSVExportButton({
  data,
  formatter,
  headers,
  filename = "export.csv",
  onExportStart,
  onExportComplete,
  onExportError,
  children = "Download CSV Export",
  ...props
}: CSVExportButtonProps) {
  const [isExporting, setIsExporting] = useState(false);

  const handleExport = () => {
    try {
      setIsExporting(true);
      onExportStart?.();

      // Format the data if a formatter is provided
      const formattedData = formatter ? formatter(data) : data;

      // Export the data
      exportToCSV(formattedData, headers, filename);

      onExportComplete?.();
    } catch (error) {
      console.error("Error exporting data:", error);
      onExportError?.(error as Error);
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={handleExport}
      disabled={isExporting || !data.length}
      {...props}
    >
      <Download className="mr-2 h-4 w-4" />
      {children}
      {isExporting && "..."}
    </Button>
  );
}
